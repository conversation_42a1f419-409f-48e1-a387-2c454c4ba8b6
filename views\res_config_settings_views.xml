<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record
        id="res_config_settings_view_form_planning_custom"
        model="ir.ui.view"
    >
        <field
            name="name"
        >res.config.settings.view.form.inherit.planning.custom</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="56" />
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath
                expr="(//block[@name='planning']/following-sibling::block)[1]"
                position="after"
            >
                <block>
                    <setting>
                        <field name="schedule_interval" />
                        <field name="planning_interval_unit" />
                    </setting>
                </block>
            </xpath>
        </field>
    </record>
</odoo>
