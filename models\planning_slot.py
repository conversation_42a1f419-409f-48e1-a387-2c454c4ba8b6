from datetime import datetime, time, timedelta

from odoo import api, fields, models


class PlanningSlot(models.Model):
    _inherit = "planning.slot"

    # Add a domain to project_id field
    project_id = fields.Many2one(
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]"
    )
    task_id = fields.Many2one(
        "project.task",
        string="Task",
        domain="[('project_id', '=', project_id)]",
    )

    def prepare_timesheet_values(self, slot, start_local, end_local):
        """
        Prepare the list of timesheet values for a given slot and its local start/end datetimes.

        Splits the time into 'morning' and 'afternoon' if the slot spans both periods.
        A slot before 12:00 is considered 'morning'; after 13:00 is 'afternoon'.
        """
        timesheet_vals = []

        def build_vals(start, end, moment):
            return {
                "task_id": slot.task_id.id,
                "project_id": slot.project_id.id if slot.project_id else False,
                "date": start.date(),
                "unit_amount": (end - start).total_seconds() / 3600.0,
                "start_hour": start.strftime("%H:%M:%S"),
                "end_hour": end.strftime("%H:%M:%S"),
                "moment": moment,
                "employee_id": (slot.resource_id or slot.employee_id).id
                if (slot.resource_id or slot.employee_id)
                else False,
                "slot_id": slot.id,
            }

        current_day = start_local.date()
        while current_day <= end_local.date():
            day_start = datetime.combine(current_day, start_local.time())
            day_end = datetime.combine(current_day, end_local.time())
            morning_end = datetime.combine(current_day, time(12, 0, 0))
            afternoon_start = datetime.combine(current_day, time(13, 0, 0))

            # Morning
            if day_start < morning_end:
                morning_start = day_start
                morning_end_cut = min(day_end, morning_end)
                if morning_start < morning_end_cut:
                    timesheet_vals.append(
                        build_vals(morning_start, morning_end_cut, "morning")
                    )

            # Afternoon
            if day_end > afternoon_start:
                afternoon_start_cut = max(day_start, afternoon_start)
                if afternoon_start_cut < day_end:
                    timesheet_vals.append(
                        build_vals(afternoon_start_cut, day_end, "afternoon")
                    )

            current_day += timedelta(days=1)

        return timesheet_vals

    # ------------------------------------------------------------
    # ORM overrides
    # ------------------------------------------------------------

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override create to automatically publish and generate timesheets from slots.
        """
        # Set default state to 'published'
        for vals in vals_list:
            vals["state"] = "published"

        records = super().create(vals_list)

        for slot in records:
            if not slot.task_id or not slot.start_datetime or not slot.end_datetime:
                continue

            # Adjust time to local timezone
            start_local = slot.start_datetime + timedelta(hours=1)
            end_local = slot.end_datetime + timedelta(hours=1)

            timesheet_vals = self.prepare_timesheet_values(slot, start_local, end_local)

            # Assign timesheets to the slot
            slot.timesheet_ids = [(0, 0, vals) for vals in timesheet_vals]

        return records

    def write(self, vals):
        """
        Override write to update timesheets when slot data changes.
        """
        res = super().write(vals)

        for slot in self:
            if not slot.task_id or not slot.start_datetime or not slot.end_datetime:
                continue

            start_local = slot.start_datetime + timedelta(hours=1)
            end_local = slot.end_datetime + timedelta(hours=1)

            # Recompute timesheet values
            timesheet_vals = self.prepare_timesheet_values(slot, start_local, end_local)

            # Remove old timesheet lines linked to this slot
            self.env["account.analytic.line"].search(
                [("slot_id", "=", slot.id)]
            ).unlink()

            # Create new timesheet lines
            for vals in timesheet_vals:
                self.env["account.analytic.line"].create(vals)

        return res

    def unlink(self):
        for slot in self:
            slot.timesheet_ids.unlink()
        return super().unlink()

    # -------------------------------------------------------------------------
    # ONCHANGE METHODS
    # -------------------------------------------------------------------------

    @api.onchange("project_id")
    def _onchange_project_id(self):
        if self.project_id:
            first_task = self.env["project.task"].search(
                [("project_id", "=", self.project_id.id)],
                order="create_date asc",
                limit=1,
            )
            self.task_id = first_task or False

    def _get_portal_domain(self):
        """Domain for employee portal users"""
        # Include all companies for the employee to allow cross-company planning visibility
        return [
            ("employee_id", "=", self.env.user.employee_id.id),
            "|",
            ("company_id", "=", False),
            ("company_id", "in", self.env.user.company_ids.ids)
        ]
