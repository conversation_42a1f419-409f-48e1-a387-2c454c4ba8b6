# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning_custom
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 14:46+0000\n"
"PO-Revision-Date: 2025-05-16 14:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_project_task__employee_ids
msgid "Employees"
msgstr "Employés"

#. module: planning_custom
#: model:ir.model.fields,help:planning_custom.field_project_task__employee_ids
msgid "Employees assigned to this task through planning slots"
msgstr "Les employés assignés à cette tâche par la poste du planning"

#. module: planning_custom
#: model:ir.model,name:planning_custom.model_planning_slot
msgid "Planning Shift"
msgstr "Poste du planning"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_planning_slot__project_id
msgid "Project"
msgstr "Projet"

#. module: planning_custom
#: model:ir.model,name:planning_custom.model_project_task
msgid "Task"
msgstr "Tâche"

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__res_config_settings__planning_interval_unit__day
msgid "Day"
msgstr "Jour(s)"

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__res_config_settings__planning_interval_unit__month
msgid "Month"
msgstr "Mois"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_res_config_settings__planning_interval_unit
msgid "Unit of schedule interval"
msgstr ""

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_res_config_settings__schedule_interval
msgid "Viewing interval of the schedule"
msgstr "Intervalle de visualisation du planning"

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__res_config_settings__planning_interval_unit__week
msgid "Week"
msgstr "Semaine(s)"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_home_planning
msgid "Check your work schedule and shifts"
msgstr "Consultez votre planning et vos postes"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_home_planning
msgid "Your Planning"
msgstr "Votre planning"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_breadcrumbs_planning
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_home_planning
msgid "Your Planning"
msgstr "Votre planning"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "Shifts from"
msgstr "Postes de"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "to"
msgstr "au"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "Role"
msgstr "Rôle"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "I am unavailable"
msgstr "Je suis indisponible"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "Ask to Switch"
msgstr "Demander à changer"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid "Cancel Switch"
msgstr "Annuler le changement"

#. module: planning_custom
#: model_terms:ir.ui.view,arch_db:planning_custom.portal_my_planning
msgid ""
"This open shift is no longer available, or the planning has been updated in the meantime.\n"
"                            Please contact your manager for further information."
msgstr ""
"Ce poste ouvert n'est plus disponible ou le planning a été mis à jour entre-"
"temps. Veuillez contacter votre responsable pour plus d'informations."
#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__start_hour
msgid "Start Hour"
msgstr "Heure Début"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__end_hour
msgid "End Hour"
msgstr "Heure Fin"

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__account_analytic_line__moment__morning
msgid "Morning"
msgstr "Matin"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__moment
msgid "Moment"
msgstr ""

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__account_analytic_line__moment__afternoon
msgid "Afternoon"
msgstr "Après-midi"
#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__start_hour
msgid "Start Hour"
msgstr "Heure Début"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__end_hour
msgid "End Hour"
msgstr "Heure Fin"

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__account_analytic_line__moment__morning
msgid "Morning"
msgstr "Matin"

#. module: planning_custom
#: model:ir.model.fields,field_description:planning_custom.field_account_analytic_line__moment
msgid "Moment"
msgstr ""

#. module: planning_custom
#: model:ir.model.fields.selection,name:planning_custom.selection__account_analytic_line__moment__afternoon
msgid "Afternoon"
msgstr "Après-midi"

#. module: planning_custom
#: model:ir.model,name:planning_custom.model_project_task
#: model:ir.model.fields,field_description:planning_custom.field_planning_slot__task_id
msgid "Task"
msgstr "Tâche"