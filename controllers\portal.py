from datetime import datetime

from dateutil.relativedelta import relativedelta

from odoo import fields, http
from odoo.http import request

from odoo.addons.planning.controllers.main import ShiftController
from odoo.addons.portal.controllers.portal import CustomerPortal


class PlanningPortalController(ShiftController):
    @http.route(
        ["/my/planning", "/my/planning/page/<int:page>"],
        type="http",
        auth="user",
        website=True,
    )
    def my_planning(self, **kwargs):
        """Portal page for employees to view their planning"""
        employee = request.env.user.employee_id
        if not employee:
            return request.redirect("/my")

        # Default to current month if no dates specified
        today = fields.Date.today()
        start_datetime = end_datetime = kwargs.get(
            "start_datetime", datetime(today.year, today.month, today.day)
        )
        # Get config parameters
        interval_value = int(
            request.env["ir.config_parameter"]
            .sudo()
            .get_param("planning_custom.schedule_interval")
        )
        interval_unit = (
            request.env["ir.config_parameter"]
            .sudo()
            .get_param("planning_custom.planning_interval_unit")
        )
        # Compute end_datetime dynamically
        if interval_unit == "day":
            end_datetime = start_datetime + relativedelta(days=interval_value)
        elif interval_unit == "week":
            end_datetime = start_datetime + relativedelta(weeks=interval_value)
        elif interval_unit == "month":
            end_datetime = start_datetime + relativedelta(months=interval_value)

        planning_sudo = request.env["planning.planning"].sudo().search([], limit=1)
        if not planning_sudo:
            planning_sudo = (
                request.env["planning.planning"]
                .sudo()
                .create(
                    {
                        "start_datetime": start_datetime,
                        "end_datetime": end_datetime,
                        "include_unassigned": True,
                    }
                )
            )
        else:
            planning_sudo.sudo().write(
                {
                    "start_datetime": start_datetime,
                    "end_datetime": end_datetime,
                }
            )

        # Get planning data using existing method from parent class
        planning_data = super()._planning_get(
            planning_sudo.access_token, employee.employee_token, False
        )

        if not planning_data:
            return request.not_found()

        planning_data["planning_sudo"] = planning_sudo
        planning_data["page_name"] = "planning"
        return request.render("planning_custom.portal_my_planning", planning_data)


class PlanningCustomerPortal(CustomerPortal):
    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)
        if "planning_count" in counters:
            Planning = request.env["planning.slot"]
            domain = Planning._get_portal_domain()
            values["planning_count"] = Planning.sudo().search_count(domain)
        return values
