from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    schedule_interval = fields.Integer(
        string="Viewing interval of the schedule",
        default="1",
        config_parameter="planning_custom.schedule_interval",
    )
    planning_interval_unit = fields.Selection(
        [("week", "Week"), ("month", "Month"), ("day", "Day")],
        string="Unit of schedule interval",
        default="month",
        config_parameter="planning_custom.planning_interval_unit",
        required=True,
    )
