from odoo import api, fields, models


class ProjectTask(models.Model):
    _inherit = "project.task"

    employee_ids = fields.Many2many(
        "hr.employee",
        string="Employees",
        readonly=True,
        help="Employees assigned to this task through planning slots",
        compute="_compute_employee_ids",
        store=True,
    )

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    @api.depends("project_id.slot_ids")
    def _compute_employee_ids(self):
        for task in self:
            slots = self.env["planning.slot"].search([("task_id", "=", task.id)])
            resources = slots.mapped("resource_id")
            employees = self.env["hr.employee"].search([
                ("resource_id", "in", resources.ids)
            ])
            task.employee_ids = employees
